import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button
} from '@mui/material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { AppForm, FormInput, SubmitButton } from '../form.elements';
import ConfigurationShell from './ConfigurationShell';
import LoaderUI from '../reusable/loaderUI';
import { AppDispatch } from '../../redux/app.store';
import { RootState } from '../../redux/reducers';
import {
  createstorageconfig,
  getlinkdata,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';
import '../../css/configuration.scss';

const CRMConfiguration = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { crmConfigDetails, isLoading }: any = useSelector(
    (state: RootState) => state.addressconfig
  );

  // State to manage edit mode
  const [isEditMode, setIsEditMode] = useState(false);

  // Check if user is authenticated
  const isAuthenticated = crmConfigDetails?.details?.is_authenticated || false;

  const validationSchema = Yup.object().shape({
    client_id: Yup.string().required('Client ID is required'),
    client_secret: Yup.string().required('Client Secret is required')
  });

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditMode(true);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditMode(false);
  };

  // Submit form Data to API
  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: crmConfigDetails
        ? crmConfigDetails?.configuration_id
        : '',
      type: 'crm',
      name: 'zoho',
      details: {
        ...crmConfigDetails?.details,
        client_id: event.client_id,
        client_secret: event.client_secret,
        redirect_uri: `${window.location.protocol}//${window.location.host}/zoho-crm/redirect`,
        is_authenticated: false
      }
    };

    let response;

    if (crmConfigDetails?.configuration_id) {
      response = await dispatch(
        updatestorageconfig({ id: crmConfigDetails?.configuration_id, data })
      );
    } else {
      response = await dispatch(createstorageconfig(data));
    }

    if (response.payload.status) {
      await dispatch(getlinkdata('crm'));
      // Reset edit mode after successful submission
      setIsEditMode(false);
    } else {
      toast.warning(
        response?.payload?.message ||
          'Something went wrong! Please try again later'
      );
    }
  };

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="sc-conatiner">
          <Typography sx={{ fontWeight: '600', fontSize: '24px' }}>
            CRM CONFIGURATION
          </Typography>
          <RadioGroup
            aria-label="crm configuration"
            name="crm-config-radio-group"
            value="zoho"
            row
          >
            <FormControlLabel
              value="zoho"
              control={<Radio />}
              label="Zoho CRM"
            />
            {/* Add more CRM options here as needed */}
          </RadioGroup>
          <Box
            sx={{
              background: '#f5f7fa',
              borderRadius: 2,
              p: 2,
              mt: 2,
              mb: 1
            }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              How to create Zoho CRM Client ID & Secret
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              1. Go to{' '}
              <a
                href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1976d2', textDecoration: 'underline' }}
              >
                Zoho CRM API Console
              </a>{' '}
              and sign in.
              <br />
              {'2. Click '}
              <b>ADD CLIENT</b>
              {' and select '}
              <b>Server-based Applications</b>.
              <br />
              3. Enter your app name and homepage URL (use your company homepage
              or <b>https://yourdomain.com</b>).
              <br />
              {'4. For '}
              <b>Authorized Redirect URIs</b>
              {', enter: '}
              <b>https://yourdomain.com/api/config/crm/oauth-callback</b>
              {' (replace with your backend callback URL).'}
              <br />
              {'5. After saving, copy the '}
              <b>Client ID</b>
              {' and '}
              <b>Client Secret</b>
              {' and paste them below.'}
              <br />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Need help? See the{' '}
              <a
                href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1976d2', textDecoration: 'underline' }}
              >
                Zoho CRM API documentation
              </a>{' '}
              or contact your admin.
            </Typography>
          </Box>

          <AppForm
            initialValues={crmConfigDetails?.details}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            <Box sx={{ p: 3 }}>
              <Box className="form-box">
                <Box className="field-input">
                  <FormInput
                    name="client_id"
                    label="Client ID"
                    required
                    placeholder=" "
                    disabled={isAuthenticated && !isEditMode}
                    containerStyles={{ width: { xs: '100%' } }}
                  />
                </Box>
                <Box className="field-input">
                  <FormInput
                    name="client_secret"
                    label="Client Secret"
                    required
                    placeholder=" "
                    disabled={isAuthenticated && !isEditMode}
                    containerStyles={{ width: { xs: '100%' } }}
                  />
                </Box>

                {/* Show different buttons based on authentication status and edit mode */}
                <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                  {/* Show Authenticate button when not authenticated OR in edit mode */}
                  {(!isAuthenticated || isEditMode) && (
                    <SubmitButton
                      title="Authenticate"
                      sx={{
                        backgroundColor: 'primaryBlue.main',
                        color: 'white2.main',
                        padding: '10px 30px',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        '&:hover': {
                          color: 'white2.main',
                          backgroundColor: 'primaryBlue.main'
                        }
                      }}
                    />
                  )}

                  {/* Show Edit button when authenticated and not in edit mode */}
                  {isAuthenticated && !isEditMode && (
                    <Button
                      variant="contained"
                      onClick={handleEditClick}
                      sx={{
                        backgroundColor: 'secondary.main',
                        color: 'white',
                        padding: '10px 30px',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        '&:hover': {
                          backgroundColor: 'secondary.dark'
                        }
                      }}
                    >
                      Edit Credentials
                    </Button>
                  )}

                  {/* Show Cancel button when in edit mode */}
                  {isEditMode && (
                    <Button
                      variant="outlined"
                      onClick={handleCancelEdit}
                      sx={{
                        color: 'text.secondary',
                        borderColor: 'text.secondary',
                        padding: '10px 30px',
                        '&:hover': {
                          borderColor: 'text.primary',
                          color: 'text.primary'
                        }
                      }}
                    >
                      Cancel
                    </Button>
                  )}
                </Box>

                {/* Show authentication status */}
                {isAuthenticated && !isEditMode && (
                  <Box
                    sx={{
                      mt: 2,
                      p: 2,
                      backgroundColor: '#d4edda',
                      borderRadius: 1
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ color: '#155724', fontWeight: 600 }}
                    >
                      ✅ Successfully authenticated with Zoho CRM
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#155724' }}>
                      Click &quot;Edit Credentials&quot; to update your
                      authentication details.
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </AppForm>
        </Box>
      )}
    </ConfigurationShell>
  );
};

export default CRMConfiguration;
