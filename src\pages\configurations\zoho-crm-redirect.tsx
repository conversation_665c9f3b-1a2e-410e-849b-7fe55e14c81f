import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material';
import { useDispatch } from 'react-redux';
import { getlinkredirectdata } from '../../redux/reducers/addressconfig.reducer';
import ConfigurationShell from '../../components/configurations/ConfigurationShell';

const ZohoCrmRedirect = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const code = params.get('code');
    const errorParam = params.get('error');
    const locationParam = params.get('location');
    const accountsServer = params.get('accounts-server');
    const orgId = localStorage.getItem('org_id');

    if (errorParam) {
      setError(`Authentication failed: ${errorParam}`);
      return;
    }
    if (!code) {
      setError('No authorization code found in redirect URL.');
      return;
    }

    // Call backend to exchange code for tokens
    dispatch(
      getlinkredirectdata({
        orgId,
        code,
        locationParam,
        accountsServer
      }) as any
    )
      .then((res: any) => {
        if (res?.payload?.status) {
          navigate('/configurations/crm-configuration', { replace: true });
        } else {
          setError(
            res?.payload?.message || 'Authentication failed. Please try again.'
          );
        }
      })
      .catch(() => {
        setError('Authentication failed. Please try again.');
      });
  }, [navigate, dispatch]);

  return (
    <ConfigurationShell>
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <CircularProgress sx={{ mb: 2 }} />
        <Typography variant="h6">
          {error || 'Authenticating with Zoho CRM...'}
        </Typography>
      </Box>
    </ConfigurationShell>
  );
};

export default ZohoCrmRedirect;
