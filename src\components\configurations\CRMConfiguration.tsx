import {
  <PERSON>,
  Typo<PERSON>,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Switch,
  Divider
} from '@mui/material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { AppForm, FormInput, SubmitButton } from '../form.elements';
import ConfigurationShell from './ConfigurationShell';
import LoaderUI from '../reusable/loaderUI';
import { AppDispatch } from '../../redux/app.store';
import { RootState } from '../../redux/reducers';
import {
  createstorageconfig,
  getlinkdata,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';
import '../../css/configuration.scss';

const CRMConfiguration = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { crmConfigDetails, isLoading }: any = useSelector(
    (state: RootState) => state.addressconfig
  );

  // State to manage edit mode
  const [isEditMode, setIsEditMode] = useState(false);

  // Check if user is authenticated
  const isAuthenticated = crmConfigDetails?.details?.is_authenticated || false;

  // Webhook configuration states
  const [webhookConfig, setWebhookConfig] = useState({
    leadToClientSync:
      crmConfigDetails?.details?.webhook_lead_to_client || false,
    clientToLeadSync: crmConfigDetails?.details?.webhook_client_to_lead || false
  });

  const validationSchema = Yup.object().shape({
    client_id: Yup.string().required('Client ID is required'),
    client_secret: Yup.string().required('Client Secret is required')
  });

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditMode(true);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditMode(false);
  };

  // Handle webhook configuration changes
  const handleWebhookChange =
    (type: 'leadToClientSync' | 'clientToLeadSync') =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setWebhookConfig((prev) => ({
        ...prev,
        [type]: event.target.checked
      }));
    };

  // Submit form Data to API
  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: crmConfigDetails
        ? crmConfigDetails?.configuration_id
        : '',
      type: 'crm',
      name: 'zoho',
      details: {
        ...crmConfigDetails?.details,
        client_id: event.client_id,
        client_secret: event.client_secret,
        redirect_uri: `${window.location.protocol}//${window.location.host}/zoho-crm/redirect`,
        is_authenticated: false,
        webhook_lead_to_client: webhookConfig.leadToClientSync,
        webhook_client_to_lead: webhookConfig.clientToLeadSync
      }
    };

    let response;

    if (crmConfigDetails?.configuration_id) {
      response = await dispatch(
        updatestorageconfig({ id: crmConfigDetails?.configuration_id, data })
      );
    } else {
      response = await dispatch(createstorageconfig(data));
    }

    if (response.payload.status) {
      await dispatch(getlinkdata('crm'));
      // Reset edit mode after successful submission
      setIsEditMode(false);
    } else {
      toast.warning(
        response?.payload?.message ||
          'Something went wrong! Please try again later'
      );
    }
  };

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box className="sc-conatiner" sx={{ pb: 6 }}>
          <Typography sx={{ fontWeight: '600', fontSize: '24px' }}>
            CRM CONFIGURATION
          </Typography>
          <RadioGroup
            aria-label="crm configuration"
            name="crm-config-radio-group"
            value="zoho"
            row
          >
            <FormControlLabel
              value="zoho"
              control={<Radio />}
              label="Zoho CRM"
            />
            {/* Add more CRM options here as needed */}
          </RadioGroup>
          <Box
            sx={{
              background: '#f5f7fa',
              borderRadius: 2,
              p: 2,
              mt: 2,
              mb: 1
            }}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              How to create Zoho CRM Client ID & Secret
            </Typography>
            <Typography variant="body2" sx={{ mb: 1 }}>
              1. Go to{' '}
              <a
                href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1976d2', textDecoration: 'underline' }}
              >
                Zoho CRM API Console
              </a>{' '}
              and sign in.
              <br />
              {'2. Click '}
              <b>ADD CLIENT</b>
              {' and select '}
              <b>Server-based Applications</b>.
              <br />
              3. Enter your app name and homepage URL (use your company homepage
              or <b>https://yourdomain.com</b>).
              <br />
              {'4. For '}
              <b>Authorized Redirect URIs</b>
              {', enter: '}
              <b>https://yourdomain.com/api/config/crm/oauth-callback</b>
              {' (replace with your backend callback URL).'}
              <br />
              {'5. After saving, copy the '}
              <b>Client ID</b>
              {' and '}
              <b>Client Secret</b>
              {' and paste them below.'}
              <br />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Need help? See the{' '}
              <a
                href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                target="_blank"
                rel="noopener noreferrer"
                style={{ color: '#1976d2', textDecoration: 'underline' }}
              >
                Zoho CRM API documentation
              </a>{' '}
              or contact your admin.
            </Typography>
          </Box>

          <AppForm
            initialValues={crmConfigDetails?.details}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            <Box sx={{ p: 3 }}>
              <Box className="form-box">
                <Box className="field-input">
                  <FormInput
                    name="client_id"
                    label="Client ID"
                    required
                    placeholder=" "
                    disabled={isAuthenticated && !isEditMode}
                    containerStyles={{ width: { xs: '100%' } }}
                  />
                </Box>
                <Box className="field-input">
                  <FormInput
                    name="client_secret"
                    label="Client Secret"
                    required
                    placeholder=" "
                    disabled={isAuthenticated && !isEditMode}
                    containerStyles={{ width: { xs: '100%' } }}
                  />
                </Box>

                {/* Show different buttons based on authentication status and edit mode */}
                <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                  {/* Show Authenticate button when not authenticated OR in edit mode */}
                  {(!isAuthenticated || isEditMode) && (
                    <SubmitButton
                      title="Authenticate"
                      sx={{
                        backgroundColor: 'primaryBlue.main',
                        color: 'white2.main',
                        padding: '10px 30px',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        '&:hover': {
                          color: 'white2.main',
                          backgroundColor: 'primaryBlue.main'
                        }
                      }}
                    />
                  )}

                  {/* Show Edit button when authenticated and not in edit mode */}
                  {isAuthenticated && !isEditMode && (
                    <Button
                      variant="contained"
                      onClick={handleEditClick}
                      sx={{
                        backgroundColor: 'secondary.main',
                        color: 'white',
                        padding: '10px 30px',
                        boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                        '&:hover': {
                          backgroundColor: 'secondary.dark'
                        }
                      }}
                    >
                      Edit Credentials
                    </Button>
                  )}

                  {/* Show Cancel button when in edit mode */}
                  {isEditMode && (
                    <Button
                      variant="outlined"
                      onClick={handleCancelEdit}
                      sx={{
                        color: 'text.secondary',
                        borderColor: 'text.secondary',
                        padding: '10px 30px',
                        '&:hover': {
                          borderColor: 'text.primary',
                          color: 'text.primary'
                        }
                      }}
                    >
                      Cancel
                    </Button>
                  )}
                </Box>

                {/* Show authentication status */}
                {isAuthenticated && !isEditMode && (
                  <Box
                    sx={{
                      mt: 2,
                      p: 2,
                      backgroundColor: '#d4edda',
                      borderRadius: 1
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ color: '#155724', fontWeight: 600 }}
                    >
                      ✅ Successfully authenticated with Zoho CRM
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#155724' }}>
                      Click &quot;Edit Credentials&quot; to update your
                      authentication details.
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </AppForm>

          {/* Webhook Configuration Section */}
          <Box sx={{ mt: 4 }}>
            <Divider sx={{ mb: 3 }} />
            <Typography sx={{ fontWeight: '600', fontSize: '20px', mb: 2 }}>
              Webhook Integration Settings
            </Typography>
            <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
              Configure automatic data synchronization between your system and
              Zoho CRM.
            </Typography>

            {/* Lead to Client Sync */}
            <Box
              sx={{ mb: 3, p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2
                }}
              >
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Lead Status Change → Client Creation
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    When a lead status changes in Zoho CRM, automatically create
                    a client in your system.
                  </Typography>
                </Box>
                <Switch
                  checked={webhookConfig.leadToClientSync}
                  onChange={handleWebhookChange('leadToClientSync')}
                  color="primary"
                />
              </Box>
              {webhookConfig.leadToClientSync && (
                <Box sx={{ p: 2, backgroundColor: '#e3f2fd', borderRadius: 1 }}>
                  <Typography
                    variant="caption"
                    sx={{ color: '#1565c0', fontWeight: 600 }}
                  >
                    ℹ️ Webhook URL: {window.location.protocol}//
                    {window.location.host}/api/webhook/zoho-lead-status
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ display: 'block', mt: 1, color: '#1565c0' }}
                  >
                    Configure this URL in your Zoho CRM webhook settings for
                    lead status changes.
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Client to Lead Sync */}
            <Box
              sx={{ mb: 3, p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2
                }}
              >
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Client Creation → Lead Creation
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    When a client is created in your system, automatically
                    create a lead in Zoho CRM.
                  </Typography>
                </Box>
                <Switch
                  checked={webhookConfig.clientToLeadSync}
                  onChange={handleWebhookChange('clientToLeadSync')}
                  color="primary"
                />
              </Box>
              {webhookConfig.clientToLeadSync && (
                <Box sx={{ p: 2, backgroundColor: '#e8f5e8', borderRadius: 1 }}>
                  <Typography
                    variant="caption"
                    sx={{ color: '#2e7d32', fontWeight: 600 }}
                  >
                    ✅ Automatic lead creation enabled
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ display: 'block', mt: 1, color: '#2e7d32' }}
                  >
                    New clients will be automatically saved as leads in your
                    Zoho CRM.
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Save Webhook Configuration Button */}
            <Box
              sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4, mb: 4 }}
            >
              <Button
                variant="contained"
                onClick={() => {
                  // Trigger form submission to save webhook config
                  const formEvent = {
                    client_id: crmConfigDetails?.details?.client_id,
                    client_secret: crmConfigDetails?.details?.client_secret
                  };
                  handleSubmit(formEvent);
                }}
                disabled={!isAuthenticated}
                sx={{
                  backgroundColor: 'primary.main',
                  color: 'white',
                  padding: '12px 40px',
                  fontSize: '16px',
                  fontWeight: 600,
                  boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                    boxShadow: '0px 6px 12px 2px rgba(0,0,0,0.3)'
                  },
                  '&:disabled': {
                    backgroundColor: 'grey.300',
                    color: 'grey.500'
                  }
                }}
              >
                Save Webhook Settings
              </Button>
            </Box>

            {!isAuthenticated && (
              <Box
                sx={{
                  mt: 2,
                  mb: 4,
                  p: 3,
                  backgroundColor: '#fff3cd',
                  borderRadius: 1,
                  border: '1px solid #ffeaa7'
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ color: '#856404', fontWeight: 600 }}
                >
                  ⚠️ Please authenticate with Zoho CRM first to enable webhook
                  configurations.
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      )}
    </ConfigurationShell>
  );
};

export default CRMConfiguration;
