import api, { apiRoutes, authHeaders, handleError } from './config';

export const getAddressConfig = async (
  _data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}?type=address`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getStorageConfig = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}?type=storage`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getCRMConfiguration = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}?type=crm`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const createStorageConfig = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(apiRoutes.configurations, data, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updateStorageConfig = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.configurations}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getLinkData = async (
  type: string,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(
      `${apiRoutes.configurations}/login-link?type=${type}`,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getEmailConfig = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}?type=email`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
export const getOneDriveFolder = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}/folders`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getLinkRedirectData = async (
  payload: {
    orgId: any;
    code: any;
    locationParam?: any;
    accountsServer?: any;
  },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();

    let url = `${apiRoutes.configurations}/redirect/${payload.orgId}?code=${payload.code}`;

    if (payload?.locationParam) {
      url += `&location=${encodeURIComponent(payload.locationParam)}`;
    }

    if (payload?.accountsServer) {
      url += `&accounts_server=${encodeURIComponent(payload.accountsServer)}&type=crm`;
    }

    const response = await api.get(url, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const selectOneDriveFolder = async (
  payload: { configuration_id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.configurations}/${payload.configuration_id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const postPdfConfig = async (
  data: any,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.post(
      `${apiRoutes.configurations}?type=pdf`,
      data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const getPdfConfig = async (
  _: null,
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.get(`${apiRoutes.configurations}?type=pdf`, {
      headers
    });
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};

export const updatePdfConfig = async (
  payload: { id: any; data: any },
  { rejectWithValue, fulfillWithValue }: any
) => {
  try {
    const headers = await authHeaders();
    const response = await api.patch(
      `${apiRoutes.configurations}/${payload.id}`,
      payload.data,
      {
        headers
      }
    );
    return fulfillWithValue(response.data);
  } catch (error) {
    return handleError(error, rejectWithValue);
  }
};
