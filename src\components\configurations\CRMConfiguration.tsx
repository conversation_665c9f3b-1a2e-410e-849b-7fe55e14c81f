import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio
} from '@mui/material';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { AppForm, FormInput, SubmitButton } from '../form.elements';
import ConfigurationShell from './ConfigurationShell';
import LoaderUI from '../reusable/loaderUI';
import { AppDispatch } from '../../redux/app.store';
import { RootState } from '../../redux/reducers';
import {
  createstorageconfig,
  getlinkdata,
  updatestorageconfig
} from '../../redux/reducers/addressconfig.reducer';

const CRMConfiguration = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { crmConfigDetails, isLoading }: any = useSelector(
    (state: RootState) => state.addressconfig
  );

  const validationSchema = Yup.object().shape({
    client_id: Yup.string().required('Client ID is required'),
    client_secret: Yup.string().required('Client Secret is required')
  });

  // Submit form Data to API
  const handleSubmit = async (event: any) => {
    const data = {
      configuration_id: crmConfigDetails
        ? crmConfigDetails?.configuration_id
        : '',
      type: 'crm',
      name: 'zoho',
      details: {
        ...crmConfigDetails?.details,
        client_id: event.client_id,
        client_secret: event.client_secret,
        redirect_uri: `${window.location.protocol}//${window.location.host}/zoho-crm/redirect`,
        is_authenticated: false
      }
    };

    let response;

    if (crmConfigDetails?.configuration_id) {
      response = await dispatch(
        updatestorageconfig({ id: crmConfigDetails?.configuration_id, data })
      );
    } else {
      response = await dispatch(createstorageconfig(data));
    }

    if (response.payload.status) {
      await dispatch(getlinkdata('crm'));
    } else {
      toast.warning(
        response?.payload?.message ||
          'Something went wrong! Please try again later'
      );
    }
  };

  return (
    <ConfigurationShell>
      {isLoading && <LoaderUI />}
      {!isLoading && (
        <Box sx={{ width: '100%', typography: 'body1' }}>
          <Box sx={{ padding: '0px 30px' }}>
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                fontWeight: 600,
                fontSize: 20,
                margin: '0 0 17px',
                color: '#27292D'
              }}
            >
              CRM CONFIGURATION
            </Typography>
            <RadioGroup
              aria-label="crm configuration"
              name="crm-config-radio-group"
              value="zoho"
              row
            >
              <FormControlLabel
                value="zoho"
                control={<Radio />}
                label="Zoho CRM"
              />
              {/* Add more CRM options here as needed */}
            </RadioGroup>
            <Box
              sx={{
                background: '#f5f7fa',
                borderRadius: 2,
                p: 2,
                mt: 2,
                mb: 1
              }}
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                How to create Zoho CRM Client ID & Secret
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                1. Go to{' '}
                <a
                  href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#1976d2', textDecoration: 'underline' }}
                >
                  Zoho CRM API Console
                </a>{' '}
                and sign in.
                <br />
                {'2. Click '}
                <b>ADD CLIENT</b>
                {' and select '}
                <b>Server-based Applications</b>.
                <br />
                3. Enter your app name and homepage URL (use your company
                homepage or <b>https://yourdomain.com</b>).
                <br />
                {'4. For '}
                <b>Authorized Redirect URIs</b>
                {', enter: '}
                <b>https://yourdomain.com/api/config/crm/oauth-callback</b>
                {' (replace with your backend callback URL).'}
                <br />
                {'5. After saving, copy the '}
                <b>Client ID</b>
                {' and '}
                <b>Client Secret</b>
                {' and paste them below.'}
                <br />
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Need help? See the{' '}
                <a
                  href="https://www.zoho.com/crm/developer/docs/api/v8/register-client.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: '#1976d2', textDecoration: 'underline' }}
                >
                  Zoho CRM API documentation
                </a>{' '}
                or contact your admin.
              </Typography>
            </Box>

            <AppForm
              initialValues={crmConfigDetails?.details}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              <Box sx={{ p: 3 }}>
                <Box className="form-box">
                  <Box className="field-input">
                    <FormInput
                      name="client_id"
                      label="Client ID"
                      required
                      placeholder=" "
                      containerStyles={{ width: { xs: '100%' } }}
                    />
                  </Box>
                  <Box className="field-input">
                    <FormInput
                      name="client_secret"
                      label="Client Secret"
                      required
                      placeholder=" "
                      containerStyles={{ width: { xs: '100%' } }}
                    />
                  </Box>
                </Box>
                <Box>
                  <SubmitButton
                    title="Authenticate"
                    sx={{
                      backgroundColor: 'primaryBlue.main',
                      color: 'white2.main',
                      padding: '10px 30px',
                      boxShadow: '0px 4px 8px 2px rgba(0,0,0,0.2)',
                      '&:hover': {
                        color: 'white2.main',
                        backgroundColor: 'primaryBlue.main'
                      }
                    }}
                  />
                </Box>
              </Box>
            </AppForm>
          </Box>
        </Box>
      )}
    </ConfigurationShell>
  );
};

export default CRMConfiguration;
