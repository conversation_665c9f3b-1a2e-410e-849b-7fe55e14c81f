// Global Imports
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

// Local Imports
import CRMConfiguration from '../../components/configurations/CRMConfiguration';
import { AppDispatch } from '../../redux/app.store';
import { fetchCRMConfiguration } from '../../redux/reducers/addressconfig.reducer';

export default function CRMConfigurationPage() {
  const dispatch = useDispatch<AppDispatch>();

  const fetchCrmConfiguration = async () => {
    try {
      await dispatch(fetchCRMConfiguration(null));
    } catch (error) {
      toast.error('Something Went Wrong, Please Try Again Later.');
    }
  };

  useEffect(() => {
    fetchCrmConfiguration();
  }, []);

  return <CRMConfiguration />;
}
